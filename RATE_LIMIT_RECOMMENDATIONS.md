# 接口限速配置建议

## 🔴 高风险接口（必须加限速）

### 1. 认证相关接口

```typescript
// /api/web3-auth/nonce - 获取签名随机数
const nonceRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 10, // 每5分钟最多10次
  message: { ok: false, message: "获取签名随机数过于频繁" }
});

// /api/web3-auth/login - Web3登录验证
const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 每15分钟最多20次登录尝试
  message: { ok: false, message: "登录尝试过于频繁" }
});
```

### 2. 支付和交易接口

```typescript
// /api/iap/payment/create - 创建IAP支付订单
const paymentCreateRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 5, // 每分钟最多5次支付创建
  message: { ok: false, message: "创建支付订单过于频繁" }
});

// /api/phrs-payment/purchase - PHRS购买道具
const phrsPurchaseRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 10, // 每分钟最多10次购买
  message: { ok: false, message: "PHRS购买过于频繁" }
});

// /api/telegram-payment/create-star-invoice - 创建Telegram支付
const telegramPaymentRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 3, // 每5分钟最多3次
  message: { ok: false, message: "创建Telegram支付过于频繁" }
});
```

### 3. 提现接口

```typescript
// 提现接口 - 严格限制
const withdrawalRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每小时最多3次提现
  message: { ok: false, message: "提现操作过于频繁，请稍后再试" }
});

// 应用到所有提现接口
// /api/withdrawal/usd
// /api/withdrawal/ton  
// /api/withdrawal/moof
```

### 4. 管理员操作

```typescript
// /api/admin/phrs-price/update - 手动触发价格更新
const priceUpdateRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 2, // 每5分钟最多2次
  message: { ok: false, message: "价格更新操作过于频繁" }
});

// /api/admin/phrs-price/sync-update - 同步更新价格
const syncUpdateRateLimit = rateLimit({
  windowMs: 10 * 60 * 1000, // 10分钟
  max: 1, // 每10分钟最多1次
  message: { ok: false, message: "同步更新操作过于频繁" }
});
```

## 🟡 中风险接口（建议加限速）

### 1. 游戏核心操作

```typescript
// /api/game-loop/process - 游戏循环处理
const gameProcessRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 60, // 每分钟最多60次（1秒1次）
  message: { ok: false, message: "游戏操作过于频繁" }
});

// /api/game-loop/offline-earnings - 离线收益计算
const offlineEarningsRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 10, // 每5分钟最多10次
  message: { ok: false, message: "离线收益计算过于频繁" }
});

// /api/farm/farm-plots/collect-all - 收集所有牧场牛奶
const collectAllRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次
  message: { ok: false, message: "收集操作过于频繁" }
});

// /api/wallet/batch-update-resources - 批量更新资源
const batchUpdateRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次
  message: { ok: false, message: "资源更新过于频繁" }
});
```

### 2. 道具和任务

```typescript
// /api/iap/boosters/use - 使用道具
const useBoosterRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 20, // 每分钟最多20次
  message: { ok: false, message: "使用道具过于频繁" }
});

// /api/new-tasks/claim - 领取任务奖励
const claimTaskRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 10, // 每分钟最多10次
  message: { ok: false, message: "领取任务奖励过于频繁" }
});
```

### 3. 邮箱验证

```typescript
// /api/user/send-email-code - 发送邮箱验证码
const emailCodeRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 3, // 每5分钟最多3次
  message: { ok: false, message: "发送验证码过于频繁" }
});

// /api/user/bind-email - 绑定邮箱
const bindEmailRateLimit = rateLimit({
  windowMs: 10 * 60 * 1000, // 10分钟
  max: 5, // 每10分钟最多5次
  message: { ok: false, message: "绑定邮箱操作过于频繁" }
});
```

## 🟢 低风险接口（可选加限速）

```typescript
// /api/game-loop/collect-data - 用户行为数据收集
const collectDataRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 100, // 每分钟最多100次
  message: { ok: false, message: "数据收集过于频繁" }
});
```

## 📋 实施优先级

### 第一阶段（立即实施）
1. ✅ 提现接口限速
2. ✅ 支付接口限速
3. ✅ 认证接口限速
4. ✅ 管理员操作限速

### 第二阶段（1周内实施）
1. ✅ 游戏核心操作限速
2. ✅ 邮箱验证限速
3. ✅ 道具使用限速

### 第三阶段（可选）
1. ✅ 数据收集接口限速
2. ✅ 其他低风险接口限速

## 🛠️ 实施建议

### 1. 创建统一的限速中间件

```typescript
// src/middlewares/rateLimitMiddleware.ts
import rateLimit from 'express-rate-limit';

export const createRateLimit = (options: {
  windowMs: number;
  max: number;
  message: string;
  skipSuccessfulRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: { ok: false, message: options.message },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    handler: (req, res) => {
      res.status(429).json({
        ok: false,
        message: options.message
      });
    }
  });
};
```

### 2. 按用户限速（推荐）

对于已认证的接口，建议使用基于用户的限速：

```typescript
const userBasedRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000,
  max: 30,
  keyGenerator: (req) => {
    // 对已认证用户使用用户ID，未认证使用IP
    return req.user?.userId || req.ip;
  }
});
```

### 3. 监控和告警

```typescript
const monitoredRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000,
  max: 30,
  onLimitReached: (req, res, options) => {
    logger.warn('Rate limit reached', {
      ip: req.ip,
      path: req.path,
      userAgent: req.headers['user-agent'],
      userId: req.user?.userId
    });
  }
});
```

## 🎯 总结

### 当前状态
- ✅ **已有限速**: TON认证、测试接口、部分管理员操作
- ❌ **缺少限速**: 支付、提现、Web3认证、游戏核心操作

### 风险评估
- **高风险**: 15个接口需要立即加限速
- **中风险**: 8个接口建议加限速
- **低风险**: 2个接口可选加限速

### 建议行动
1. **立即实施**: 提现、支付、认证接口限速
2. **本周内**: 游戏操作、邮箱验证限速
3. **监控**: 添加限速触发日志和告警

这样的限速配置可以有效防止：
- 🛡️ 恶意攻击和滥用
- 💰 支付欺诈和重复提现
- 🎮 游戏作弊和资源滥用
- 📧 垃圾邮件和验证码轰炸
